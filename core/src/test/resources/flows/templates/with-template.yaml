id: with-template
namespace: io.kestra.tests

inputs:
  - id: with-string
    type: STRING
  - id: with-optional
    type: STRING
    required: false

variables:
  var-1: var-1
  var-2: '{{ var-1 }}'

taskDefaults:
  - type: io.kestra.plugin.core.log.Log
    values:
      level: "ERROR"

tasks:
  - id: 1-return
    type: io.kestra.plugin.core.debug.Return
    format: "{{task.id}} > {{taskrun.startDate}}"
  - id: 2-template
    type: io.kestra.plugin.core.flow.Template
    namespace: io.kestra.tests
    templateId: template
    args:
      my-forward: "{{ inputs['with-string'] }}"
  - id: 3-end
    type: io.kestra.plugin.core.debug.Return
    format: "{{task.id}} > {{taskrun.startDate}}"
