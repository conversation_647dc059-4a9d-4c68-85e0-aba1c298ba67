<svg width="284" height="127" viewBox="0 0 284 127" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_748_7152)">
<mask id="path-1-inside-1_748_7152" fill="white">
<path d="M0 8.88745C0 4.46917 3.58172 0.887451 8 0.887451H275.5C279.918 0.887451 283.5 4.46917 283.5 8.88745V126.887H0V8.88745Z"/>
</mask>
<path d="M0 8.88745C0 4.46917 3.58172 0.887451 8 0.887451H275.5C279.918 0.887451 283.5 4.46917 283.5 8.88745V126.887H0V8.88745Z" fill="#8C4BFF"/>
<circle cx="141.922" cy="64.0212" r="36.8523" stroke="white" stroke-width="0.475306" stroke-dasharray="0.95 0.95"/>
<path d="M183.302 68.6603C183.063 71.354 182.563 74.0547 181.788 76.7279L181.63 76.6804C181.254 77.9774 180.813 79.268 180.306 80.5482C179.799 81.8283 179.236 83.0691 178.622 84.2689L178.768 84.3456C177.503 86.8184 176.021 89.1175 174.356 91.2285L174.227 91.1249C172.528 93.2795 170.637 95.2372 168.591 96.9825L168.698 97.1092C166.615 98.8859 164.372 100.443 162.007 101.765L161.926 101.62C159.556 102.944 157.064 104.03 154.486 104.862L154.537 105.02C151.954 105.854 149.285 106.434 146.571 106.742L146.551 106.577C143.875 106.881 141.154 106.92 138.425 106.679L138.412 106.844C135.732 106.606 133.044 106.099 130.383 105.307L130.429 105.148C129.137 104.764 127.852 104.312 126.578 103.791C125.303 103.27 124.069 102.692 122.875 102.06L122.799 102.207C120.341 100.906 118.057 99.3803 115.962 97.6655L116.065 97.5374C113.932 95.7915 111.997 93.8487 110.275 91.7466L110.148 91.8514C108.401 89.7191 106.872 87.4237 105.578 85.0043L105.723 84.9266C104.432 82.5132 103.376 79.9758 102.57 77.3535L102.413 77.4014C101.609 74.7843 101.055 72.0831 100.765 69.3364L100.928 69.3201C100.644 66.6221 100.616 63.88 100.861 61.1307L100.697 61.1146C100.936 58.4209 101.436 55.7202 102.211 53.047L102.369 53.0945C102.745 51.7975 103.186 50.5069 103.693 49.2267C104.201 47.9466 104.763 46.7058 105.377 45.506L105.231 45.4293C106.496 42.9565 107.978 40.6574 109.643 38.5464L109.772 38.65C111.471 36.4954 113.362 34.5377 115.408 32.7924L115.301 32.6657C117.384 30.889 119.627 29.3316 121.992 28.0101L122.073 28.155C124.443 26.8309 126.936 25.7445 129.513 24.9125L129.462 24.7548C132.045 23.9207 134.714 23.3412 137.428 23.033L137.448 23.1975C140.124 22.8937 142.845 22.8545 145.574 23.0962L145.587 22.9312C148.268 23.1686 150.955 23.6757 153.616 24.4677L153.57 24.6265C154.862 25.011 156.147 25.4628 157.421 25.9839C158.696 26.5049 159.931 27.0832 161.124 27.7147L161.2 27.5682C163.658 28.869 165.943 30.3946 168.037 32.1094L167.934 32.2375C170.067 33.9834 172.002 35.9262 173.725 38.0283L173.851 37.9235C175.598 40.0558 177.127 42.3512 178.421 44.7706L178.276 44.8483C179.567 47.2617 180.623 49.7992 181.429 52.4214L181.586 52.3736C182.39 54.9906 182.945 57.6918 183.234 60.4385L183.071 60.4548C183.355 63.1528 183.383 65.8949 183.139 68.6442L183.302 68.6603Z" stroke="url(#paint0_linear_748_7152)" stroke-width="0.329909" stroke-linejoin="round" stroke-dasharray="7.92 7.92"/>
<g filter="url(#filter0_d_748_7152)">
<path d="M154.922 94.3462C152.376 95.4266 149.663 96.1814 146.833 96.5592L146.811 96.3957C145.445 96.5781 144.052 96.6722 142.637 96.6722C141.222 96.6722 139.829 96.5781 138.463 96.3957L138.441 96.5592C135.611 96.1814 132.898 95.4266 130.352 94.3462L130.417 94.1944C127.832 93.0973 125.421 91.6626 123.236 89.9445L123.134 90.0742C120.932 88.3425 118.959 86.3245 117.27 84.0751L117.402 83.976C115.739 81.7619 114.353 79.3223 113.294 76.7105L113.142 76.7725C112.105 74.2152 111.382 71.4936 111.021 68.6568L111.184 68.6359C111.01 67.2689 110.92 65.875 110.92 64.4597C110.92 63.0444 111.01 61.6505 111.184 60.2835L111.021 60.2626C111.382 57.4258 112.105 54.7042 113.142 52.147L113.294 52.2089C114.353 49.5971 115.739 47.1576 117.402 44.9434L117.27 44.8443C118.959 42.5949 120.932 40.5769 123.134 38.8453L123.236 38.9749C125.421 37.2568 127.832 35.8221 130.417 34.7251L130.352 34.5732C132.898 33.4928 135.611 32.738 138.441 32.3602L138.463 32.5237C139.829 32.3413 141.222 32.2472 142.637 32.2472C144.052 32.2472 145.445 32.3413 146.811 32.5237L146.833 32.3602C149.663 32.738 152.376 33.4928 154.922 34.5732L154.857 34.7251C157.442 35.8221 159.854 37.2568 162.039 38.9749L162.141 38.8453C164.342 40.5769 166.315 42.5949 168.004 44.8443L167.872 44.9434C169.535 47.1576 170.922 49.5971 171.98 52.2089L172.133 52.147C173.169 54.7042 173.892 57.4258 174.254 60.2626L174.09 60.2835C174.264 61.6505 174.354 63.0444 174.354 64.4597C174.354 65.875 174.264 67.269 174.09 68.6359L174.254 68.6568C173.892 71.4936 173.169 74.2152 172.133 76.7725L171.98 76.7105C170.922 79.3224 169.535 81.7619 167.872 83.976L168.004 84.0751C166.315 86.3245 164.342 88.3425 162.141 90.0742L162.039 89.9445C159.854 91.6626 157.442 93.0973 154.857 94.1944L154.922 94.3462Z" stroke="#5F00E8" stroke-width="0.329909" stroke-linejoin="round" stroke-dasharray="7.92 7.92" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_f_748_7152)">
<rect width="170.248" height="266.493" transform="matrix(-1 0 0 1 271.874 -29.359)" fill="url(#paint1_linear_748_7152)"/>
</g>
<g filter="url(#filter2_f_748_7152)">
<rect width="170.248" height="266.493" transform="matrix(-1 0 0 1 356.998 3.64111)" fill="url(#paint2_linear_748_7152)"/>
</g>
<g filter="url(#filter3_d_748_7152)">
<path d="M151.375 63.8875C158.25 63.8875 163.75 69.3875 163.75 76.2625C163.75 78.6824 163.062 80.965 161.852 82.8624L170.322 91.3875L166.5 95.2099L157.92 86.7674C156.022 87.95 153.768 88.6375 151.375 88.6375C144.5 88.6375 139 83.1375 139 76.2625C139 69.3875 144.5 63.8875 151.375 63.8875ZM151.375 69.3875C149.552 69.3875 147.803 70.1118 146.514 71.4011C145.224 72.6904 144.5 74.4391 144.5 76.2625C144.5 78.0858 145.224 79.8345 146.514 81.1238C147.803 82.4131 149.552 83.1375 151.375 83.1375C153.198 83.1375 154.947 82.4131 156.236 81.1238C157.526 79.8345 158.25 78.0858 158.25 76.2625C158.25 74.4391 157.526 72.6904 156.236 71.4011C154.947 70.1118 153.198 69.3875 151.375 69.3875ZM128 72.1375V77.6375H133.5C133.885 81.9 135.7 85.7225 138.478 88.6375H122.5C119.447 88.6375 117 86.1625 117 83.1375V44.6375C117 41.585 119.447 39.1375 122.5 39.1375H161C162.459 39.1375 163.858 39.7169 164.889 40.7484C165.921 41.7798 166.5 43.1788 166.5 44.6375V66.7199C163.338 61.715 157.755 58.3875 151.375 58.3875C147.882 58.3875 144.61 59.405 141.86 61.1375H128V66.6375H136.25C135.26 68.2875 134.435 70.1575 133.968 72.1375H128ZM155.5 55.6375V50.1375H128V55.6375H155.5Z" fill="white"/>
</g>
</g>
<path d="M-1 8.88745C-1 3.91689 3.02944 -0.112549 8 -0.112549H275.5C280.471 -0.112549 284.5 3.91689 284.5 8.88745H282.5C282.5 5.02146 279.366 1.88745 275.5 1.88745H8C4.13401 1.88745 1 5.02146 1 8.88745H-1ZM283.5 126.887H0H283.5ZM-1 126.887V8.88745C-1 3.91689 3.02944 -0.112549 8 -0.112549V1.88745C4.13401 1.88745 1 5.02146 1 8.88745V126.887H-1ZM275.5 -0.112549C280.471 -0.112549 284.5 3.91689 284.5 8.88745V126.887H282.5V8.88745C282.5 5.02146 279.366 1.88745 275.5 1.88745V-0.112549Z" fill="#404559" mask="url(#path-1-inside-1_748_7152)"/>
<defs>
<filter id="filter0_d_748_7152" x="109.766" y="32.0823" width="65.7431" height="66.7343" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.989728"/>
<feGaussianBlur stdDeviation="0.494864"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.432266 0 0 0 0 0.00354165 0 0 0 0 0.846458 0 0 0 1 0"/>
<feBlend mode="screen" in2="BackgroundImageFix" result="effect1_dropShadow_748_7152"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_748_7152" result="shape"/>
</filter>
<filter id="filter1_f_748_7152" x="-98.3735" y="-229.359" width="570.248" height="666.493" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_748_7152"/>
</filter>
<filter id="filter2_f_748_7152" x="-13.25" y="-196.359" width="570.248" height="666.493" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_748_7152"/>
</filter>
<filter id="filter3_d_748_7152" x="88.75" y="14.8875" width="106" height="106" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.960784 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_748_7152"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_748_7152" result="shape"/>
</filter>
<linearGradient id="paint0_linear_748_7152" x1="124.863" y1="29.3466" x2="164.175" y2="120.016" gradientUnits="userSpaceOnUse">
<stop offset="0.015625" stop-color="#DCC2FF"/>
<stop offset="0.130208" stop-color="#B8D3FD"/>
<stop offset="0.234375" stop-color="#BD90FF"/>
<stop offset="0.505208" stop-color="#61D9FF" stop-opacity="0"/>
<stop offset="0.765625" stop-color="#E7B5FF" stop-opacity="0.84"/>
</linearGradient>
<linearGradient id="paint1_linear_748_7152" x1="85.1238" y1="0" x2="85.1238" y2="266.493" gradientUnits="userSpaceOnUse">
<stop stop-color="#6218FF" stop-opacity="0"/>
<stop offset="1" stop-color="#6117FF"/>
</linearGradient>
<linearGradient id="paint2_linear_748_7152" x1="85.1238" y1="0" x2="85.1238" y2="266.493" gradientUnits="userSpaceOnUse">
<stop stop-color="#6218FF" stop-opacity="0"/>
<stop offset="1" stop-color="#E22F8F"/>
</linearGradient>
<clipPath id="clip0_748_7152">
<path d="M0 8.88745C0 4.46917 3.58172 0.887451 8 0.887451H275.5C279.918 0.887451 283.5 4.46917 283.5 8.88745V126.887H0V8.88745Z" fill="white"/>
</clipPath>
</defs>
</svg>
